{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/admin/addmenu.tsx", "../../src/admin/editmenu.tsx", "../../src/admin/orders.tsx", "../../src/admin/restaurant.tsx", "../../src/auth/forgotpassword.tsx", "../../src/auth/login.tsx", "../../src/auth/resetpassword.tsx", "../../src/auth/signup.tsx", "../../src/auth/verifyemail.tsx", "../../src/components/availablemenu.tsx", "../../src/components/cart.tsx", "../../src/components/checkoutconfirmpage.tsx", "../../src/components/filterpage.tsx", "../../src/components/footer.tsx", "../../src/components/heresection.tsx", "../../src/components/loading.tsx", "../../src/components/navbar.tsx", "../../src/components/profile.tsx", "../../src/components/restaurantdetail.tsx", "../../src/components/searchpage.tsx", "../../src/components/success.tsx", "../../src/components/ui/aspect-ratio.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/menubar.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/sonner.tsx", "../../src/components/ui/table.tsx", "../../src/layout/mainlayout.tsx", "../../src/lib/utils.ts", "../../src/schema/menuschema.ts", "../../src/schema/restaurantschema.ts", "../../src/schema/userschema.ts", "../../src/store/usecartstore.ts", "../../src/store/usemenustore.ts", "../../src/store/useorderstore.ts", "../../src/store/userestaurantstore.ts", "../../src/store/usethemestore.ts", "../../src/store/useuserstore.ts", "../../src/types/carttype.ts", "../../src/types/ordertype.ts", "../../src/types/restauranttype.ts"], "errors": true, "version": "5.8.3"}