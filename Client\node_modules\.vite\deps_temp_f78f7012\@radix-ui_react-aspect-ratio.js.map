{"version": 3, "sources": ["../../@radix-ui/react-aspect-ratio/src/aspect-ratio.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * AspectRatio\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'AspectRatio';\n\ntype AspectRatioElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AspectRatioProps extends PrimitiveDivProps {\n  ratio?: number;\n}\n\nconst AspectRatio = React.forwardRef<AspectRatioElement, AspectRatioProps>(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return (\n      <div\n        style={{\n          // ensures inner element is contained\n          position: 'relative',\n          // ensures padding bottom trick maths works\n          width: '100%',\n          paddingBottom: `${100 / ratio}%`,\n        }}\n        data-radix-aspect-ratio-wrapper=\"\"\n      >\n        <Primitive.div\n          {...aspectRatioProps}\n          ref={forwardedRef}\n          style={{\n            ...style,\n            // ensures children expand in ratio\n            position: 'absolute',\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n          }}\n        />\n      </div>\n    );\n  }\n);\n\nAspectRatio.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = AspectRatio;\n\nexport {\n  AspectRatio,\n  //\n  Root,\n};\nexport type { AspectRatioProps };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,YAAuB;AA6Bf,yBAAA;AAtBR,IAAM,OAAO;AAQb,IAAM,cAAoB;EACxB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,QAAQ,IAAI,GAAG,OAAO,GAAG,iBAAiB,IAAI;AACtD,eACE;MAAC;MAAA;QACC,OAAO;;UAEL,UAAU;;UAEV,OAAO;UACP,eAAe,GAAG,MAAM,KAAK;QAC/B;QACA,mCAAgC;QAEhC,cAAA;UAAC,UAAU;UAAV;YACE,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG;;cAEH,UAAU;cACV,KAAK;cACL,OAAO;cACP,QAAQ;cACR,MAAM;YACR;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,IAAM,OAAO;", "names": []}