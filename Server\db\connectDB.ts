import mongoose from "mongoose";
import { exit } from "process";

const connectDB = async () => {
    try {
        if (!process.env.MONGO_URI) {
            throw new Error("MONGO_URI environment variable not defined");
        }
        // Removed hardcoded dbUrl for security reasons. Use only environment variable.
        const conn = await mongoose.connect(process.env.MONGO_URI, {
            retryWrites: true,
            w: 'majority'
        });

        console.log(`MongoDB Connected: ${conn.connection.host}`);
    } catch (error) {
        console.error('Database connection error:', error);
        exit(1); // Exit process with failure
    }
}
export default connectDB;
