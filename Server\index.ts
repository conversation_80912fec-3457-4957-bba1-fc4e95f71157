import express from "express";
import dotenv from "dotenv";
import connectDB from "./db/connectDB";
import bodyParser from "body-parser";
import cookieParser from "cookie-parser";
import cors from "cors";
import userRoute from "./router/user.route";
import restaurantRoute from "./router/restaurant.route";
import menuRoute from "./router/menu.route";
import orderRoute from "./router/order.route";
import path from "path";

dotenv.config();

const app = express();

const PORT = process.env.PORT || 3000;

const DIRNAME = path.resolve();

// default middleware for any mern project
app.use(bodyParser.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.json());
app.use(cookieParser());
const corsOptions = {
    // Use environment variable for CORS origin with fallback
    origin: process.env.CORS_ORIGIN || "http://localhost:5173",
    credentials: true
}
app.use(cors(corsOptions));

// api
app.use("/api/v1/user", userRoute);
app.use("/api/v1/restaurant", restaurantRoute);
app.use("/api/v1/menu", menuRoute);
app.use("/api/v1/order", orderRoute);

// Static file serving disabled for development
// app.use(express.static(path.join(DIRNAME,"/client/dist")));
// app.use("*path",(_,res) => {
//     res.sendFile(path.resolve(DIRNAME, "client","dist","index.html"));
// });

// Await connectDB before starting server
const startServer = async () => {
    try {
        await connectDB();
        app.listen(PORT, () => {
            console.log(`Server listening at port ${PORT}`);
        });
    } catch (error) {
        console.error("Failed to connect to database", error);
        process.exit(1);
    }
};

startServer();

// Global error handling middleware
app.use((err: any, req: any, res: any, next: any) => {
    console.error("Global error handler:", err);
    res.status(500).json({ message: "Internal server error" });
});
