import { Request, Response, NextFunction, RequestHandler } from "express";
import jwt from "jsonwebtoken";

// 👇 Type correctly as Express middleware
export const isAuthenticated: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const token = req.cookies.token;

  if (!token) {
    res.status(401).json({
      success: false,
      message: "User not authenticated",
    });
    return;
  }

  if (!process.env.SECRET_KEY) {
    res.status(500).json({
      success: false,
      message: "SECRET_KEY not defined",
    });
    return;
  }

  try {
    const decode = jwt.verify(token, process.env.SECRET_KEY) as jwt.JwtPayload;
    (req as any).id = decode.userId;
    next(); // ✔ continue to next middleware
  } catch (error) {
    console.error("Auth error:", error);
    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({ success: false, message: "Token expired" });
    } else {
      res.status(401).json({ success: false, message: "Invalid token" });
    }
  }
};

// export const isAuthenticated = async (req: Request, res: Response, next: NextFunction) => {
//     try {
//         const token = req.cookies.token;
//         if (!token) {
//             return res.status(401).json({
//                 success: false,
//                 message: "User not authenticated"
//             });
//         }

//         if (!process.env.SECRET_KEY) {
//             return res.status(500).json({
//                 success: false,
//                 message: "Server configuration error: SECRET_KEY not defined"
//             });
//         }

//         // verify the token
//         const decode = jwt.verify(token, process.env.SECRET_KEY) as jwt.JwtPayload;

//         req.id = decode.userId;
//         next();
//     } catch (error) {
//         console.error('Authentication error:', error);
//         if (error instanceof jwt.JsonWebTokenError) {
//             return res.status(401).json({
//                 success: false,
//                 message: "Invalid token"
//             });
//         } else if (error instanceof jwt.TokenExpiredError) {
//             return res.status(401).json({
//                 success: false,
//                 message: "Token has expired"
//             });
//         }
//         return res.status(500).json({
//             success: false,
//             message: "Internal server error"
//         });
//     }
// }
